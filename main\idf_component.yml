## IDF Component Manager Manifest File
dependencies:
  waveshare/esp_lcd_sh8601: 1.0.2
  espressif/esp_lcd_ili9341: ==1.2.0
  espressif/esp_lcd_gc9a01: ==2.0.1
  espressif/esp_lcd_st77916: ^1.0.1
  espressif/esp_lcd_st7796: 
    version: 1.3.2
    rules:
    - if: target not in [esp32c3, esp32c2]
  espressif/esp_lcd_spd2010: ==1.0.2
  espressif/esp_io_expander_tca9554: ==2.0.0
  espressif/esp_lcd_panel_io_additions: ^1.0.1
  78/esp_lcd_nv3023: ~1.0.0
  78/esp-wifi-connect: ~2.4.2
  78/esp-opus-encoder: ~2.3.3
  78/esp-ml307: ~2.1.5
  78/xiaozhi-fonts: ~1.3.2
  espressif/led_strip: ^2.5.5
  espressif/esp_codec_dev: ~1.3.2
  espressif/esp-sr: 
    version: ~2.1.1
    rules:
      - if: target in [esp32, esp32s3, esp32c3, esp32c5]
  espressif/button: ~4.1.3
  espressif/knob: ^1.0.0
  espressif/esp32-camera: ^2.0.15
  espressif/esp_lcd_touch_ft5x06: ~1.0.7
  espressif/esp_lcd_touch_gt911: ^1
  waveshare/esp_lcd_touch_cst9217: ^1.0.3
  lvgl/lvgl: ~9.2.2
  esp_lvgl_port: ~2.6.0
  espressif/esp_io_expander_tca95xx_16bit: ^2.0.0
  espressif2022/image_player: ^1.1.0
  espressif/adc_mic:
    version: ^0.2.0
    rules:
    - if: target not in [esp32c2]
  espressif/esp_mmap_assets: ">=1.2"

  tny-robotics/sh1106-esp-idf:
    version: ^1.0.0
    rules:
    - if: idf_version >= "5.4.0"

  waveshare/esp_lcd_jd9365_10_1:
    version: '*'
    rules:
    - if: target in [esp32p4]
  waveshare/esp_lcd_st7703:
    version: '*'
    rules:
      - if: target in [esp32p4]
  espressif/esp_lcd_ili9881c:
    version: ^1.0.1
    rules:
    - if: target in [esp32p4]
  espressif/esp_wifi_remote:
    version: '*'
    rules:
    - if: target in [esp32p4]
  lijunru-hub/servo_dog_ctrl:
    version: '^0.1.4'
    rules:
    - if: target in [esp32c3]

  ## Required IDF version
  idf:
    version: '>=5.4.0'
